<%
// Galerie-Konfiguration mit Standardwerten
const galleryConfig = {
    images: locals.galleryImages || [],
    totalImages: locals.galleryTotalImages || (locals.galleryImages ? locals.galleryImages.length : 0),
    title: locals.galleryTitle || 'Bilder',
    showLoadMore: locals.galleryShowLoadMore !== false,
    loadMoreText: locals.galleryLoadMoreText || 'Mehr laden',
    imagePath: locals.galleryImagePath || '/uploads/activity_photos',
    imageSize: locals.galleryImageSize || '_m.jpeg',
    fullImageSize: locals.galleryFullImageSize || '_o.jpeg',
    gridId: locals.galleryGridId || 'imagesGrid',
    modalId: locals.galleryModalId || 'imageModal',
    loadMoreBtnId: locals.galleryLoadMoreBtnId || 'loadMoreImagesBtn',
    loadingId: locals.galleryLoadingId || 'loadingMoreImages',
    showActivityName: locals.galleryShowActivityName !== false,
    showCaption: locals.galleryShowCaption !== false,
    showDate: locals.galleryShowDate !== false,
    dateFormat: locals.galleryDateFormat || 'de-DE'
};
%>

<% if (galleryConfig.images && galleryConfig.images.length > 0) { %>
    <div class="images-container">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
            <h3 style="margin: 0;"><%= galleryConfig.title %> (<%= galleryConfig.totalImages %>)</h3>
            <% if (galleryConfig.showLoadMore && galleryConfig.totalImages > 12) { %>
                <button id="<%= galleryConfig.loadMoreBtnId %>" class="button button-secondary" style="padding: 8px 12px; font-size: 0.9em;">
                    <%= galleryConfig.loadMoreText %> (<%= galleryConfig.totalImages - 12 %> weitere)
                </button>
            <% } %>
        </div>
        <div class="images-grid" id="<%= galleryConfig.gridId %>">
            <% galleryConfig.images.forEach((image, index) => { %>
                <div class="image-item" onclick="openImageModal(<%- index %>)">
                    <img src="<%= galleryConfig.imagePath %>/<%= image.activity_id %>/<%= image.base_filename %><%= galleryConfig.imageSize %>" 
                         alt="<%= image.caption || image.activity_name %>" 
                         loading="lazy">
                    <div class="image-overlay">
                        <div class="image-info">
                            <% if (galleryConfig.showActivityName && image.activity_name) { %>
                                <div class="image-activity"><%= image.activity_name %></div>
                            <% } %>
                            <% if (galleryConfig.showCaption && image.caption) { %>
                                <div class="image-caption"><%= image.caption %></div>
                            <% } %>
                            <% if (galleryConfig.showDate && image.start_date_local) { %>
                                <div class="image-date"><%= new Date(image.start_date_local).toLocaleDateString(galleryConfig.dateFormat) %></div>
                            <% } %>
                        </div>
                    </div>
                </div>
            <% }); %>
        </div>
        <% if (galleryConfig.showLoadMore) { %>
            <div id="<%= galleryConfig.loadingId %>" style="display: none; text-align: center; padding: 20px;">
                <p>Lade weitere Bilder...</p>
            </div>
        <% } %>
    </div>
<% } %>

<!-- Image Modal -->
<div id="<%= galleryConfig.modalId %>" class="image-modal">
    <div class="image-modal-content">
        <span class="image-modal-close">&times;</span>

        <!-- Navigation Arrows -->
        <div class="image-modal-nav image-modal-prev" id="modalPrevBtn">
            <span>&#8249;</span>
        </div>
        <div class="image-modal-nav image-modal-next" id="modalNextBtn">
            <span>&#8250;</span>
        </div>

        <!-- Clickable Areas for Navigation -->
        <div class="image-modal-click-area image-modal-click-left" id="modalClickLeft"></div>
        <div class="image-modal-click-area image-modal-click-right" id="modalClickRight"></div>

        <!-- Loading Spinner -->
        <div class="image-modal-loading" id="modalLoading">
            <div class="loading-spinner"></div>
        </div>

        <img id="modalImage" src="" alt="" style="display: none;">

        <!-- Image Counter -->
        <div class="image-modal-counter" id="modalImageCounter">
            <span id="modalCurrentIndex">1</span> / <span id="modalTotalImages">1</span>
        </div>

        <div class="image-modal-info">
            <div id="modalImageActivity"></div>
            <div id="modalImageCaption"></div>
            <div id="modalImageDate"></div>
        </div>
    </div>
</div>
