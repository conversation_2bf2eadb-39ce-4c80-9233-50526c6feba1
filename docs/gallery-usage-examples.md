# Image Gallery - Verwendungsbeispiele

## 1. Trip-An<PERSON><PERSON> (bereits implementiert)

```ejs
<!-- views/users/trip_detail.ejs -->

<!-- CSS und JavaScript einbinden -->
<link rel="stylesheet" href="/css/image-gallery.css">
<script src="/js/image-gallery.js"></script>

<!-- Galerie-Konfiguration -->
<%
locals.galleryImages = images;
locals.galleryTotalImages = totalImages || (images ? images.length : 0);
locals.galleryTitle = 'Bilder der Reise';
locals.galleryShowLoadMore = true;
locals.galleryLoadMoreText = 'Mehr laden';
%>
<%- include('../partials/image-gallery') %>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const galleryConfig = {
        images: allImages,
        totalImages: totalImages,
        loadMoreApiUrl: isOwner
            ? `/user/api/trip/${tripId}/images?page={page}&limit=12`
            : `/show/api/public/trip/${shareUuid}/images?page={page}&limit=12`,
        loadMoreText: 'Mehr laden',
        dateFormat: 'de-DE'
    };
    initImageGallery(galleryConfig);
});
</script>
```

## 2. Aktivitäts-Ansicht

```ejs
<!-- views/users/activity_detail.ejs -->

<!-- CSS und JavaScript einbinden -->
<link rel="stylesheet" href="/css/image-gallery.css">
<script src="/js/image-gallery.js"></script>

<!-- Galerie-Konfiguration für Aktivität -->
<%
locals.galleryImages = activity.images || [];
locals.galleryTotalImages = activity.images ? activity.images.length : 0;
locals.galleryTitle = 'Bilder der Aktivität';
locals.galleryShowLoadMore = false; // Keine Paginierung für einzelne Aktivitäten
locals.galleryShowActivityName = false; // Aktivitätsname nicht anzeigen (ist ja bekannt)
%>
<%- include('../partials/image-gallery') %>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const galleryConfig = {
        images: <%- JSON.stringify(activity.images || []) %>,
        totalImages: <%= activity.images ? activity.images.length : 0 %>,
        loadMoreApiUrl: null, // Keine Paginierung
        dateFormat: 'de-DE'
    };
    initImageGallery(galleryConfig);
});
</script>
```

## 3. Aktivitäts-Übersicht mit Paginierung

```ejs
<!-- views/users/activities.ejs -->

<!-- CSS und JavaScript einbinden -->
<link rel="stylesheet" href="/css/image-gallery.css">
<script src="/js/image-gallery.js"></script>

<!-- Galerie-Konfiguration für Übersicht -->
<%
locals.galleryImages = recentImages || [];
locals.galleryTotalImages = totalImageCount || 0;
locals.galleryTitle = 'Neueste Bilder';
locals.galleryShowLoadMore = true;
locals.galleryLoadMoreText = 'Weitere Bilder laden';
%>
<%- include('../partials/image-gallery') %>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const galleryConfig = {
        images: <%- JSON.stringify(recentImages || []) %>,
        totalImages: <%= totalImageCount || 0 %>,
        loadMoreApiUrl: '/user/api/images?page={page}&limit=20',
        loadMoreText: 'Weitere Bilder laden',
        dateFormat: 'de-DE'
    };
    initImageGallery(galleryConfig);
});
</script>
```

## 4. POI-Ansicht

```ejs
<!-- views/users/poi_detail.ejs -->

<!-- CSS und JavaScript einbinden -->
<link rel="stylesheet" href="/css/image-gallery.css">
<script src="/js/image-gallery.js"></script>

<!-- Galerie-Konfiguration für POI -->
<%
locals.galleryImages = poi.images || [];
locals.galleryTotalImages = poi.images ? poi.images.length : 0;
locals.galleryTitle = 'Bilder des POI';
locals.galleryShowLoadMore = false;
locals.galleryShowActivityName = false; // POIs haben keine Aktivitätsnamen
locals.galleryShowDate = false; // POI-Bilder haben oft kein Datum
locals.galleryImagePath = '/uploads/poi_images'; // Anderer Pfad für POI-Bilder
%>
<%- include('../partials/image-gallery') %>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const galleryConfig = {
        images: <%- JSON.stringify(poi.images || []) %>,
        totalImages: <%= poi.images ? poi.images.length : 0 %>,
        imagePath: '/uploads/poi_images',
        loadMoreApiUrl: null,
        dateFormat: 'de-DE'
    };
    initImageGallery(galleryConfig);
});
</script>
```

## 5. Öffentliche Aktivitäts-Ansicht

```ejs
<!-- views/public/activity_detail.ejs -->

<!-- CSS und JavaScript einbinden -->
<link rel="stylesheet" href="/css/image-gallery.css">
<script src="/js/image-gallery.js"></script>

<!-- Galerie-Konfiguration für öffentliche Ansicht -->
<%
locals.galleryImages = activity.images || [];
locals.galleryTotalImages = activity.images ? activity.images.length : 0;
locals.galleryTitle = 'Bilder';
locals.galleryShowLoadMore = false;
locals.galleryShowActivityName = false;
%>
<%- include('../partials/image-gallery') %>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const galleryConfig = {
        images: <%- JSON.stringify(activity.images || []) %>,
        totalImages: <%= activity.images ? activity.images.length : 0 %>,
        loadMoreApiUrl: null,
        dateFormat: 'de-DE'
    };
    initImageGallery(galleryConfig);
});
</script>
```

## 6. Angepasste Galerie mit eigenen IDs

```ejs
<!-- Für mehrere Galerien auf einer Seite -->

<!-- Erste Galerie -->
<%
locals.galleryImages = activityImages;
locals.galleryTotalImages = activityImages.length;
locals.galleryTitle = 'Aktivitäts-Bilder';
locals.galleryGridId = 'activityImagesGrid';
locals.galleryModalId = 'activityImageModal';
locals.galleryLoadMoreBtnId = 'loadMoreActivityImagesBtn';
locals.galleryLoadingId = 'loadingMoreActivityImages';
%>
<%- include('../partials/image-gallery') %>

<!-- Zweite Galerie -->
<%
locals.galleryImages = poiImages;
locals.galleryTotalImages = poiImages.length;
locals.galleryTitle = 'POI-Bilder';
locals.galleryGridId = 'poiImagesGrid';
locals.galleryModalId = 'poiImageModal';
locals.galleryLoadMoreBtnId = 'loadMorePoiImagesBtn';
locals.galleryLoadingId = 'loadingMorePoiImages';
locals.galleryImagePath = '/uploads/poi_images';
%>
<%- include('../partials/image-gallery') %>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Erste Galerie
    initImageGallery({
        images: <%- JSON.stringify(activityImages) %>,
        totalImages: <%= activityImages.length %>,
        gridId: 'activityImagesGrid',
        modalId: 'activityImageModal',
        loadMoreBtnId: 'loadMoreActivityImagesBtn',
        loadingId: 'loadingMoreActivityImages'
    });

    // Zweite Galerie
    initImageGallery({
        images: <%- JSON.stringify(poiImages) %>,
        totalImages: <%= poiImages.length %>,
        imagePath: '/uploads/poi_images',
        gridId: 'poiImagesGrid',
        modalId: 'poiImageModal',
        loadMoreBtnId: 'loadMorePoiImagesBtn',
        loadingId: 'loadingMorePoiImages'
    });
});
</script>
```

## 7. Minimale Galerie ohne Overlays

```ejs
<!-- Einfache Galerie nur mit Bildern -->
<%
locals.galleryImages = images;
locals.galleryTotalImages = images.length;
locals.galleryTitle = 'Bilder';
locals.galleryShowLoadMore = false;
locals.galleryShowActivityName = false;
locals.galleryShowCaption = false;
locals.galleryShowDate = false;
%>
<%- include('../partials/image-gallery') %>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initImageGallery({
        images: <%- JSON.stringify(images) %>,
        totalImages: <%= images.length %>,
        loadMoreApiUrl: null
    });
});
</script>
```

## 8. Galerie mit benutzerdefinierten Bildgrößen

```ejs
<!-- Galerie mit anderen Bildgrößen -->
<%
locals.galleryImages = images;
locals.galleryTotalImages = images.length;
locals.galleryTitle = 'Hochauflösende Bilder';
locals.galleryImageSize = '_l.jpeg'; // Große Thumbnails
locals.galleryFullImageSize = '_xl.jpeg'; // Extra große Vollbilder
%>
<%- include('../partials/image-gallery') %>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initImageGallery({
        images: <%- JSON.stringify(images) %>,
        totalImages: <%= images.length %>,
        imageSize: '_l.jpeg',
        fullImageSize: '_xl.jpeg'
    });
});
</script>
```

## API-Endpunkte für "Mehr laden"

Die API-Endpunkte sollten folgendes Format zurückgeben:

```javascript
// GET /user/api/trip/123/images?page=2&limit=12
{
    "success": true,
    "images": [
        {
            "activity_id": 456,
            "base_filename": "IMG_001",
            "activity_name": "Wanderung",
            "caption": "Schöne Aussicht",
            "start_date_local": "2024-01-15T10:30:00"
        }
        // ... weitere Bilder
    ],
    "hasMore": true,
    "totalImages": 45,
    "currentPage": 2,
    "totalPages": 4
}
```

## CSS-Anpassungen

```css
/* Eigene Anpassungen in der jeweiligen Template-CSS */

/* Kleinere Thumbnails für mobile Ansicht */
@media (max-width: 480px) {
    .images-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.5rem;
    }
}

/* Größere Thumbnails für Desktop */
@media (min-width: 1200px) {
    .images-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1.5rem;
    }
}

/* Angepasste Hover-Effekte */
.image-item:hover {
    transform: scale(1.02); /* Weniger starker Zoom */
}

/* Eigene Farben für verschiedene Bereiche */
.trip-gallery .image-overlay {
    background: linear-gradient(transparent, rgba(0,123,255,0.8)); /* Blau für Trips */
}

.activity-gallery .image-overlay {
    background: linear-gradient(transparent, rgba(40,167,69,0.8)); /* Grün für Aktivitäten */
}
```
