# Image Gallery Component

Die Image Gallery ist eine wiederverwendbare Komponente für die Anzeige von Bildergalerien mit Modal-Ansicht, Navigation und "Mehr laden"-Funktionalität.

## Dateien

- **Template**: `views/partials/image-gallery.ejs`
- **CSS**: `public/css/image-gallery.css`
- **JavaScript**: `public/js/image-gallery.js`

## Verwendung

### 1. CSS und JavaScript einbinden

```html
<link rel="stylesheet" href="/css/image-gallery.css">
<script src="/js/image-gallery.js"></script>
```

### 2. Template-Konfiguration

Vor dem Include des Partials die Konfigurationsvariablen setzen:

```ejs
<%
// Galerie-Konfiguration
locals.galleryImages = images;                    // Array der Bilder
locals.galleryTotalImages = totalImages;          // Gesamtanzahl der Bilder
locals.galleryTitle = 'Bilder der Reise';        // Titel der Galerie
locals.galleryShowLoadMore = true;               // "Mehr laden" Button anzeigen
locals.galleryLoadMoreText = 'Mehr laden';       // Text für "Mehr laden" Button
locals.galleryImagePath = '/uploads/activity_photos';  // Basis-Pfad für Bilder
locals.galleryImageSize = '_m.jpeg';             // Suffix für Thumbnail-Bilder
locals.galleryFullImageSize = '_o.jpeg';         // Suffix für Vollbilder
locals.galleryGridId = 'imagesGrid';             // ID des Galerie-Grids
locals.galleryModalId = 'imageModal';            // ID des Modals
locals.galleryLoadMoreBtnId = 'loadMoreImagesBtn'; // ID des "Mehr laden" Buttons
locals.galleryLoadingId = 'loadingMoreImages';   // ID des Loading-Divs
locals.galleryShowActivityName = true;          // Aktivitätsname anzeigen
locals.galleryShowCaption = true;               // Bildunterschrift anzeigen
locals.galleryShowDate = true;                  // Datum anzeigen
locals.galleryDateFormat = 'de-DE';             // Datumsformat
%>

<%- include('../partials/image-gallery') %>
```

### 3. JavaScript-Initialisierung

```javascript
document.addEventListener('DOMContentLoaded', function() {
    const galleryConfig = {
        images: allImages,                        // Array der Bilder
        totalImages: totalImages,                 // Gesamtanzahl
        imagePath: '/uploads/activity_photos',    // Basis-Pfad
        imageSize: '_m.jpeg',                     // Thumbnail-Suffix
        fullImageSize: '_o.jpeg',                 // Vollbild-Suffix
        gridId: 'imagesGrid',                     // Grid-ID
        modalId: 'imageModal',                    // Modal-ID
        loadMoreBtnId: 'loadMoreImagesBtn',       // Button-ID
        loadingId: 'loadingMoreImages',           // Loading-ID
        loadMoreApiUrl: '/api/images?page={page}&limit=12', // API-URL
        loadMoreText: 'Mehr laden',               // Button-Text
        dateFormat: 'de-DE'                       // Datumsformat
    };

    initImageGallery(galleryConfig);
});
```

## Konfigurationsoptionen

### Template-Variablen (locals)

| Variable | Typ | Standard | Beschreibung |
|----------|-----|----------|--------------|
| `galleryImages` | Array | `[]` | Array der anzuzeigenden Bilder |
| `galleryTotalImages` | Number | `0` | Gesamtanzahl der verfügbaren Bilder |
| `galleryTitle` | String | `'Bilder'` | Titel der Galerie |
| `galleryShowLoadMore` | Boolean | `true` | "Mehr laden" Button anzeigen |
| `galleryLoadMoreText` | String | `'Mehr laden'` | Text für "Mehr laden" Button |
| `galleryImagePath` | String | `'/uploads/activity_photos'` | Basis-Pfad für Bilder |
| `galleryImageSize` | String | `'_m.jpeg'` | Suffix für Thumbnail-Bilder |
| `galleryFullImageSize` | String | `'_o.jpeg'` | Suffix für Vollbilder |
| `galleryGridId` | String | `'imagesGrid'` | ID des Galerie-Grids |
| `galleryModalId` | String | `'imageModal'` | ID des Modals |
| `galleryLoadMoreBtnId` | String | `'loadMoreImagesBtn'` | ID des "Mehr laden" Buttons |
| `galleryLoadingId` | String | `'loadingMoreImages'` | ID des Loading-Divs |
| `galleryShowActivityName` | Boolean | `true` | Aktivitätsname in Overlay anzeigen |
| `galleryShowCaption` | Boolean | `true` | Bildunterschrift in Overlay anzeigen |
| `galleryShowDate` | Boolean | `true` | Datum in Overlay anzeigen |
| `galleryDateFormat` | String | `'de-DE'` | Locale für Datumsformatierung |

### JavaScript-Konfiguration

| Option | Typ | Standard | Beschreibung |
|--------|-----|----------|--------------|
| `images` | Array | `[]` | Array der Bilder |
| `totalImages` | Number | `0` | Gesamtanzahl der Bilder |
| `imagePath` | String | `'/uploads/activity_photos'` | Basis-Pfad für Bilder |
| `imageSize` | String | `'_m.jpeg'` | Suffix für Thumbnails |
| `fullImageSize` | String | `'_o.jpeg'` | Suffix für Vollbilder |
| `gridId` | String | `'imagesGrid'` | ID des Galerie-Grids |
| `modalId` | String | `'imageModal'` | ID des Modals |
| `loadMoreBtnId` | String | `'loadMoreImagesBtn'` | ID des "Mehr laden" Buttons |
| `loadingId` | String | `'loadingMoreImages'` | ID des Loading-Divs |
| `loadMoreApiUrl` | String | `null` | API-URL für "Mehr laden" (mit `{page}` Platzhalter) |
| `loadMoreText` | String | `'Mehr laden'` | Text für "Mehr laden" Button |
| `dateFormat` | String | `'de-DE'` | Locale für Datumsformatierung |

## Bild-Datenstruktur

Jedes Bild-Objekt sollte folgende Eigenschaften haben:

```javascript
{
    activity_id: 123,                    // ID der zugehörigen Aktivität
    base_filename: 'image_name',         // Basis-Dateiname (ohne Suffix)
    activity_name: 'Wanderung',          // Name der Aktivität (optional)
    caption: 'Schöne Aussicht',          // Bildunterschrift (optional)
    start_date_local: '2024-01-15'       // Datum der Aktivität (optional)
}
```

## Features

- **Responsive Grid**: Automatische Anpassung an verschiedene Bildschirmgrößen
- **Modal-Ansicht**: Vollbild-Anzeige mit Navigation
- **Touch-Gesten**: Swipe-Navigation auf mobilen Geräten
- **Tastatur-Navigation**: Pfeiltasten und Escape-Taste
- **Lazy Loading**: Bilder werden erst bei Bedarf geladen
- **"Mehr laden"**: Paginierte Nachladung von Bildern
- **Hover-Effekte**: Informations-Overlay bei Mouse-Over
- **Loading-Spinner**: Anzeige während des Bildladens

## Anpassungen

### CSS-Anpassungen

Die Galerie kann über CSS-Variablen oder durch Überschreiben der Klassen angepasst werden:

```css
.images-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); /* Größere Thumbnails */
    gap: 2rem; /* Größerer Abstand */
}

.image-item {
    border-radius: 12px; /* Rundere Ecken */
}
```

### JavaScript-Erweiterungen

Die `ImageGallery`-Klasse kann erweitert werden:

```javascript
class CustomImageGallery extends ImageGallery {
    constructor(config) {
        super(config);
        // Eigene Anpassungen
    }
    
    // Eigene Methoden überschreiben
    showImageAtIndex(index) {
        // Eigene Implementierung
        super.showImageAtIndex(index);
    }
}
```

## Migration von bestehenden Galerien

1. CSS-Styles aus Templates entfernen
2. HTML-Modal-Code entfernen
3. JavaScript-Funktionen entfernen
4. CSS- und JavaScript-Includes hinzufügen
5. Template-Konfiguration setzen
6. JavaScript-Initialisierung hinzufügen

## Beispiel: Aktivitäts-Galerie

```ejs
<%
locals.galleryImages = activity.images;
locals.galleryTotalImages = activity.images.length;
locals.galleryTitle = 'Bilder der Aktivität';
locals.galleryShowLoadMore = false; // Keine Paginierung für Aktivitäten
%>
<%- include('../partials/image-gallery') %>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initImageGallery({
        images: <%- JSON.stringify(activity.images) %>,
        totalImages: <%= activity.images.length %>,
        loadMoreApiUrl: null // Keine Paginierung
    });
});
</script>
```
