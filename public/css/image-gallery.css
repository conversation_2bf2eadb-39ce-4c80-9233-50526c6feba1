/* Image Gallery Styles */
.images-container {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    width: 100%;
}

.images-container h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    color: #495057;
}

.images-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
}

.image-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.image-item:hover {
    transform: scale(1.05);
}

.image-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 1rem;
    transform: translateY(100%);
    transition: transform 0.2s ease;
}

.image-item:hover .image-overlay {
    transform: translateY(0);
}

.image-activity {
    font-weight: bold;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.image-caption {
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}

.image-date {
    font-size: 0.75rem;
    opacity: 0.8;
}

/* Image Modal */
.image-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.9);
}

.image-modal-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    box-sizing: border-box;
}

.image-modal img {
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
    border-radius: 8px;
}

.image-modal-close {
    position: absolute;
    top: 20px;
    right: 30px;
    color: white;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
    transition: color 0.2s ease;
}

.image-modal-close:hover {
    color: #ccc;
}

/* Navigation Arrows */
.image-modal-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255,255,255,0.2);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    transition: background-color 0.2s ease;
    z-index: 1001;
}

.image-modal-nav:hover {
    background: rgba(255,255,255,0.4);
}

.image-modal-nav.disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

.image-modal-prev {
    left: 20px;
}

.image-modal-next {
    right: 20px;
}

/* Clickable Areas for Navigation */
.image-modal-click-area {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 30%;
    cursor: pointer;
    z-index: 999;
}

.image-modal-click-left {
    left: 0;
}

.image-modal-click-right {
    right: 0;
}

/* Image Counter */
.image-modal-counter {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    z-index: 1001;
}

/* Image Info */
.image-modal-info {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    text-align: center;
    max-width: 80%;
    z-index: 1001;
}

.image-modal-info div {
    margin-bottom: 4px;
}

.image-modal-info div:last-child {
    margin-bottom: 0;
}

/* Loading Spinner */
.image-modal-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1001;
}

.loading-spinner {
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .images-container {
        background: white;
        border-radius: 4px;
        padding: 2px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .images-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .image-modal-content {
        padding: 5px;
    }

    .image-modal img {
        max-height: 90%;
    }

    /* Mobile Navigation */
    .image-modal-nav {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }

    .image-modal-prev {
        left: 10px;
    }

    .image-modal-next {
        right: 10px;
    }

    .image-modal-counter {
        top: 10px;
        font-size: 12px;
        padding: 6px 12px;
    }

    .image-modal-click-area {
        width: 35%;
    }
}
